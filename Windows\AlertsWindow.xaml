<Window x:Class="DebtManager.Windows.AlertsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تنبيهات السداد" 
        Height="500" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource WarningBrush}" Padding="20">
            <StackPanel>
                <TextBlock Text="⏰ تنبيهات السداد" 
                          Style="{StaticResource HeaderText}" 
                          Foreground="White" 
                          HorizontalAlignment="Center"/>
                <TextBlock Text="الديون التي اقترب أو تأخر موعد سدادها" 
                          FontSize="14" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filter -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="عرض الديون لـ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Name="CmbDaysFilter" Width="150" Style="{StaticResource ModernComboBox}"
                             SelectionChanged="CmbDaysFilter_SelectionChanged">
                        <ComboBoxItem Content="الأسبوع القادم (7 أيام)" Tag="7"/>
                        <ComboBoxItem Content="الأسبوعين القادمين (14 يوم)" Tag="14"/>
                        <ComboBoxItem Content="الشهر القادم (30 يوم)" Tag="30"/>
                        <ComboBoxItem Content="جميع الديون المستحقة" Tag="365"/>
                    </ComboBox>
                    <Button Name="BtnRefresh" Content="🔄 تحديث" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource InfoBrush}"
                           Margin="20,0,0,0"
                           Click="BtnRefresh_Click"/>
                </StackPanel>

                <!-- Alerts Grid -->
                <DataGrid Grid.Row="1" Name="AlertsDataGrid" 
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="80"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding FormattedAmount}" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                        <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding FormattedDueDate}" Width="120"/>
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="3" Padding="5,2">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="False">
                                                        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding DueStatus}" 
                                                  Foreground="White" 
                                                  FontWeight="SemiBold"
                                                  HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Note}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{StaticResource DarkBrush}">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="جاهز" Foreground="White"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="AlertCountText" Text="0 تنبيه" Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
