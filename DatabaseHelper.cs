using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;

namespace DebtManager
{
    public static class DatabaseHelper
    {
        private static readonly string DatabasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DebtManager.db");
        private static readonly string ConnectionString = $"Data Source={DatabasePath};Version=3;";

        public static void InitializeDatabase()
        {
            if (!File.Exists(DatabasePath))
            {
                SQLiteConnection.CreateFile(DatabasePath);
            }

            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            // إنشاء جدول الديون
            string createDebtsTable = @"
                CREATE TABLE IF NOT EXISTS Debts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Type TEXT NOT NULL, -- مورد أو عميل
                    Currency TEXT NOT NULL,
                    Amount REAL NOT NULL,
                    Date TEXT NOT NULL,
                    Status TEXT NOT NULL, -- علينا أو له
                    Note TEXT,
                    DueDate TEXT,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                )";

            // إنشاء جدول العملات
            string createCurrenciesTable = @"
                CREATE TABLE IF NOT EXISTS Currencies (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Symbol TEXT,
                    IsActive INTEGER DEFAULT 1
                )";

            // إنشاء جدول الأطراف (الموردين والعملاء)
            string createPartiesTable = @"
                CREATE TABLE IF NOT EXISTS Parties (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Type TEXT NOT NULL, -- مورد أو عميل
                    Phone TEXT,
                    Address TEXT,
                    Notes TEXT,
                    IsActive INTEGER DEFAULT 1,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                )";

            using var command = new SQLiteCommand(createDebtsTable, connection);
            command.ExecuteNonQuery();

            command.CommandText = createCurrenciesTable;
            command.ExecuteNonQuery();

            command.CommandText = createPartiesTable;
            command.ExecuteNonQuery();

            // إدراج العملات الافتراضية
            InsertDefaultCurrencies(connection);
        }

        private static void InsertDefaultCurrencies(SQLiteConnection connection)
        {
            var defaultCurrencies = new[]
            {
                ("دينار أردني", "د.أ"),
                ("دولار أمريكي", "$"),
                ("يورو", "€"),
                ("ذهب", "غرام"),
                ("ليرة تركية", "₺")
            };

            foreach (var (name, symbol) in defaultCurrencies)
            {
                string insertCurrency = @"
                    INSERT OR IGNORE INTO Currencies (Name, Symbol) 
                    VALUES (@name, @symbol)";

                using var command = new SQLiteCommand(insertCurrency, connection);
                command.Parameters.AddWithValue("@name", name);
                command.Parameters.AddWithValue("@symbol", symbol);
                command.ExecuteNonQuery();
            }
        }

        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(ConnectionString);
        }

        public static List<string> GetCurrencies()
        {
            var currencies = new List<string>();
            using var connection = GetConnection();
            connection.Open();

            string query = "SELECT Name FROM Currencies WHERE IsActive = 1 ORDER BY Name";
            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                currencies.Add(reader.GetString("Name"));
            }

            return currencies;
        }

        public static List<string> GetParties(string type = "")
        {
            var parties = new List<string>();
            using var connection = GetConnection();
            connection.Open();

            string query = "SELECT Name FROM Parties WHERE IsActive = 1";
            if (!string.IsNullOrEmpty(type))
            {
                query += " AND Type = @type";
            }
            query += " ORDER BY Name";

            using var command = new SQLiteCommand(query, connection);
            if (!string.IsNullOrEmpty(type))
            {
                command.Parameters.AddWithValue("@type", type);
            }

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                parties.Add(reader.GetString("Name"));
            }

            return parties;
        }
    }
}
