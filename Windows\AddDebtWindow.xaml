<Window x:Class="DebtManager.Windows.AddDebtWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب جديد" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="NoResize">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <TextBlock Name="HeaderText" Text="إضافة حساب جديد" 
                      Style="{StaticResource HeaderText}" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" Padding="30" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم الطرف -->
                <TextBlock Text="اسم الطرف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox Name="CmbPartyName" 
                         Style="{StaticResource ModernComboBox}"
                         IsEditable="True"
                         SelectionChanged="CmbPartyName_SelectionChanged"/>

                <!-- النوع -->
                <TextBlock Text="النوع *" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton Name="RbSupplier" Content="مورد" 
                               GroupName="PartyType" 
                               Margin="0,0,20,0"
                               Checked="PartyType_Checked"/>
                    <RadioButton Name="RbCustomer" Content="عميل" 
                               GroupName="PartyType"
                               Checked="PartyType_Checked"/>
                </StackPanel>

                <!-- العملة -->
                <TextBlock Text="العملة *" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <ComboBox Name="CmbCurrency" Style="{StaticResource ModernComboBox}"/>

                <!-- المبلغ -->
                <TextBlock Text="المبلغ *" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <TextBox Name="TxtAmount" 
                        Style="{StaticResource ModernTextBox}"
                        PreviewTextInput="TxtAmount_PreviewTextInput"/>

                <!-- الحالة -->
                <TextBlock Text="الحالة *" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <ComboBox Name="CmbStatus" Style="{StaticResource ModernComboBox}">
                    <ComboBoxItem Content="علينا" Tag="علينا"/>
                    <ComboBoxItem Content="له" Tag="له"/>
                </ComboBox>

                <!-- التاريخ -->
                <TextBlock Text="التاريخ *" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <DatePicker Name="DpDate" 
                           FontSize="14" 
                           Margin="0,5"
                           SelectedDate="{x:Static sys:DateTime.Today}"
                           xmlns:sys="clr-namespace:System;assembly=mscorlib"/>

                <!-- تاريخ الاستحقاق -->
                <TextBlock Text="تاريخ الاستحقاق" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <DatePicker Name="DpDueDate" 
                           FontSize="14" 
                           Margin="0,5"/>

                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,15,0,5"/>
                <TextBox Name="TxtNotes" 
                        Style="{StaticResource ModernTextBox}"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"/>

                <!-- معلومات إضافية للطرف الجديد -->
                <Expander Name="PartyDetailsExpander" 
                         Header="معلومات إضافية للطرف الجديد" 
                         Margin="0,20,0,0"
                         IsExpanded="False">
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="رقم الهاتف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Name="TxtPhone" Style="{StaticResource ModernTextBox}"/>
                        
                        <TextBlock Text="العنوان" FontWeight="SemiBold" Margin="0,15,0,5"/>
                        <TextBox Name="TxtAddress" 
                                Style="{StaticResource ModernTextBox}"
                                Height="60"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                        
                        <TextBlock Text="ملاحظات عن الطرف" FontWeight="SemiBold" Margin="0,15,0,5"/>
                        <TextBox Name="TxtPartyNotes" 
                                Style="{StaticResource ModernTextBox}"
                                Height="60"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnSave" Content="💾 حفظ" 
                       Style="{StaticResource ModernButton}"
                       Background="{StaticResource SuccessBrush}"
                       Width="120"
                       Click="BtnSave_Click"/>
                
                <Button Name="BtnCancel" Content="❌ إلغاء" 
                       Style="{StaticResource ModernButton}"
                       Background="{StaticResource DangerBrush}"
                       Width="120"
                       Click="BtnCancel_Click"/>
                
                <Button Name="BtnDelete" Content="🗑️ حذف" 
                       Style="{StaticResource ModernButton}"
                       Background="{StaticResource WarningBrush}"
                       Width="120"
                       Visibility="Collapsed"
                       Click="BtnDelete_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
