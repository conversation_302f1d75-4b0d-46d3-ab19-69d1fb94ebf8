using System;
using System.Data.SQLite;
using System.Windows;
using DebtManager.Models;

namespace DebtManager.Windows
{
    public partial class AddPartyWindow : Window
    {
        private readonly string _partyType;
        private readonly Party? _editingParty;
        private readonly bool _isEditMode;

        public AddPartyWindow(string partyType, Party? partyToEdit = null)
        {
            InitializeComponent();
            _partyType = partyType;
            _editingParty = partyToEdit;
            _isEditMode = partyToEdit != null;

            HeaderText.Text = _isEditMode ? 
                $"تعديل {(_partyType == "مورد" ? "المورد" : "العميل")}" : 
                $"إضافة {(_partyType == "مورد" ? "مورد" : "عميل")} جديد";

            if (_isEditMode)
            {
                LoadPartyData();
            }
        }

        private void LoadPartyData()
        {
            if (_editingParty == null) return;

            TxtName.Text = _editingParty.Name;
            TxtPhone.Text = _editingParty.Phone;
            TxtAddress.Text = _editingParty.Address;
            TxtNotes.Text = _editingParty.Notes;
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(TxtName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query;
                SQLiteCommand command;

                if (_isEditMode)
                {
                    query = @"
                        UPDATE Parties 
                        SET Name = @name, Phone = @phone, Address = @address, Notes = @notes
                        WHERE Id = @id";
                    command = new SQLiteCommand(query, connection);
                    command.Parameters.AddWithValue("@id", _editingParty!.Id);
                }
                else
                {
                    // التحقق من عدم وجود اسم مكرر
                    string checkQuery = "SELECT COUNT(*) FROM Parties WHERE Name = @name AND Type = @type AND IsActive = 1";
                    using var checkCommand = new SQLiteCommand(checkQuery, connection);
                    checkCommand.Parameters.AddWithValue("@name", TxtName.Text.Trim());
                    checkCommand.Parameters.AddWithValue("@type", _partyType);
                    
                    var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                    if (count > 0)
                    {
                        MessageBox.Show("يوجد طرف بنفس الاسم مسبقاً", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        TxtName.Focus();
                        return;
                    }

                    query = @"
                        INSERT INTO Parties (Name, Type, Phone, Address, Notes)
                        VALUES (@name, @type, @phone, @address, @notes)";
                    command = new SQLiteCommand(query, connection);
                    command.Parameters.AddWithValue("@type", _partyType);
                }

                command.Parameters.AddWithValue("@name", TxtName.Text.Trim());
                command.Parameters.AddWithValue("@phone", TxtPhone.Text.Trim());
                command.Parameters.AddWithValue("@address", TxtAddress.Text.Trim());
                command.Parameters.AddWithValue("@notes", TxtNotes.Text.Trim());

                command.ExecuteNonQuery();

                MessageBox.Show(_isEditMode ? "تم تحديث البيانات بنجاح" : "تم إضافة الطرف بنجاح", 
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
