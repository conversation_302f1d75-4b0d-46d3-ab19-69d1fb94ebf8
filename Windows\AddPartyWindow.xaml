<Window x:Class="DebtManager.Windows.AddPartyWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة طرف جديد" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="NoResize">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <TextBlock Name="HeaderText" Text="إضافة مورد جديد" 
                      Style="{StaticResource HeaderText}" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Form Content -->
        <StackPanel Grid.Row="1" Padding="30" VerticalAlignment="Center">
            <!-- الاسم -->
            <TextBlock Text="الاسم *" FontWeight="SemiBold" Margin="0,0,0,5"/>
            <TextBox Name="TxtName" Style="{StaticResource ModernTextBox}"/>

            <!-- رقم الهاتف -->
            <TextBlock Text="رقم الهاتف" FontWeight="SemiBold" Margin="0,15,0,5"/>
            <TextBox Name="TxtPhone" Style="{StaticResource ModernTextBox}"/>

            <!-- العنوان -->
            <TextBlock Text="العنوان" FontWeight="SemiBold" Margin="0,15,0,5"/>
            <TextBox Name="TxtAddress" 
                    Style="{StaticResource ModernTextBox}"
                    Height="60"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    VerticalScrollBarVisibility="Auto"/>

            <!-- ملاحظات -->
            <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,15,0,5"/>
            <TextBox Name="TxtNotes" 
                    Style="{StaticResource ModernTextBox}"
                    Height="60"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    VerticalScrollBarVisibility="Auto"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnSave" Content="💾 حفظ" 
                       Style="{StaticResource ModernButton}"
                       Background="{StaticResource SuccessBrush}"
                       Width="120"
                       Click="BtnSave_Click"/>
                
                <Button Name="BtnCancel" Content="❌ إلغاء" 
                       Style="{StaticResource ModernButton}"
                       Background="{StaticResource DangerBrush}"
                       Width="120"
                       Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
