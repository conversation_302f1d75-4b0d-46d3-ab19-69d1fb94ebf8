<Window x:Class="DebtManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="DebtManager - إدارة ديون الموردين والعملاء" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <StackPanel>
                <TextBlock Text="إدارة ديون الموردين والعملاء" 
                          Style="{StaticResource HeaderText}" 
                          Foreground="White" 
                          HorizontalAlignment="Center"/>
                <TextBlock Text="نظام شامل لإدارة الديون والمتابعة المالية" 
                          FontSize="14" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Panel -->
            <Border Grid.Column="0" Background="White" CornerRadius="10" Padding="20" Margin="0,0,20,0">
                <StackPanel>
                    <TextBlock Text="القائمة الرئيسية" Style="{StaticResource SubHeaderText}" HorizontalAlignment="Center"/>
                    
                    <Button Name="BtnAddDebt" Content="➕ إضافة حساب جديد" 
                           Style="{StaticResource ModernButton}" 
                           Background="{StaticResource SuccessBrush}"
                           Click="BtnAddDebt_Click"/>
                    
                    <Button Name="BtnSuppliers" Content="👥 إدارة الموردين" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnSuppliers_Click"/>
                    
                    <Button Name="BtnCustomers" Content="👤 إدارة العملاء" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnCustomers_Click"/>
                    
                    <Button Name="BtnCurrencies" Content="💱 إدارة العملات" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnCurrencies_Click"/>
                    
                    <Button Name="BtnAlerts" Content="⏰ التنبيهات" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource WarningBrush}"
                           Click="BtnAlerts_Click"/>
                    
                    <Button Name="BtnReports" Content="📊 التقارير" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource InfoBrush}"
                           Click="BtnReports_Click"/>
                    
                    <Separator Margin="0,20"/>
                    
                    <Button Name="BtnRefresh" Content="🔄 تحديث البيانات" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource SecondaryBrush}"
                           Click="BtnRefresh_Click"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" CornerRadius="10" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Summary Cards -->
                    <StackPanel Grid.Row="0" Name="SummaryPanel">
                        <TextBlock Text="ملخص الديون" Style="{StaticResource SubHeaderText}"/>
                        <ScrollViewer Name="SummaryScrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                            <StackPanel Name="SummaryCardsPanel" Orientation="Horizontal"/>
                        </ScrollViewer>
                    </StackPanel>

                    <!-- Data Grid -->
                    <Grid Grid.Row="1" Margin="0,20,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Filter Panel -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="تصفية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox Name="CmbFilterCurrency" Width="120" Margin="0,0,10,0" 
                                     Style="{StaticResource ModernComboBox}"
                                     SelectionChanged="CmbFilterCurrency_SelectionChanged"/>
                            <ComboBox Name="CmbFilterType" Width="100" Margin="0,0,10,0"
                                     Style="{StaticResource ModernComboBox}"
                                     SelectionChanged="CmbFilterType_SelectionChanged"/>
                            <ComboBox Name="CmbFilterStatus" Width="100" Margin="0,0,10,0"
                                     Style="{StaticResource ModernComboBox}"
                                     SelectionChanged="CmbFilterStatus_SelectionChanged"/>
                        </StackPanel>

                        <!-- Debts DataGrid -->
                        <DataGrid Grid.Row="1" Name="DebtsDataGrid" 
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 SelectionMode="Single"
                                 MouseDoubleClick="DebtsDataGrid_MouseDoubleClick">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="80"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding FormattedAmount}" Width="120"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding FormattedDate}" Width="100"/>
                                <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding FormattedDueDate}" Width="120"/>
                                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Note}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{StaticResource DarkBrush}">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="جاهز" Foreground="White"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="RecordCountText" Text="0 سجل" Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
