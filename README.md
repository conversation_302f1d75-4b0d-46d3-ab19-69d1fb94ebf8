# DebtManager - إدارة ديون الموردين والعملاء

## 📋 وصف المشروع
برنامج شامل لإدارة ديون الموردين والعملاء مطور بلغة C# باستخدام WPF و .NET 8.

## ✨ المميزات الرئيسية

### 🏠 الشاشة الرئيسية (Dashboard)
- عرض ملخص الديون حسب العملة مع الألوان التمييزية:
  - 🟢 الأخضر: ما لنا (دائن)
  - 🔴 الأحمر: ما علينا (مدين)
- جدول شامل لجميع الديون مع إمكانية التصفية
- إحصائيات فورية ومحدثة

### ➕ إدارة الديون
- إضافة ديون جديدة للموردين والعملاء
- تعديل وحذف الديون الموجودة
- دعم عملات متعددة (دينار، دولار، يورو، ذهب، ليرة تركية)
- تحديد تواريخ الاستحقاق
- إضافة ملاحظات مفصلة

### 👥 إدارة الأطراف
- إدارة بيانات الموردين والعملاء
- حفظ معلومات الاتصال والعناوين
- البحث والتصفية السريع

### 💱 إدارة العملات
- إضافة عملات جديدة
- تخصيص رموز العملات
- حذف العملات غير المستخدمة

### ⏰ نظام التنبيهات
- تنبيهات للديون المستحقة
- عرض الديون المتأخرة
- تصنيف حسب درجة الأولوية

### 📊 التقارير المالية
- تقرير الملخص العام
- تقارير مفصلة حسب العملة
- تقرير الديون المتأخرة
- إمكانية الطباعة والتصدير (قيد التطوير)

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة
- .NET 8.0 SDK أو أحدث
- Visual Studio 2022 أو Visual Studio Code
- Windows 10/11

### المكتبات المستخدمة
- **WPF**: واجهة المستخدم الرسومية
- **SQLite**: قاعدة البيانات المحلية
- **Microsoft.Data.Sqlite**: للتعامل مع قاعدة البيانات

## 🚀 كيفية التشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd DebtManager
```

### 2. بناء المشروع
```bash
dotnet restore
dotnet build
```

### 3. تشغيل التطبيق
```bash
dotnet run
```

أو افتح المشروع في Visual Studio واضغط F5.

## 📁 هيكل المشروع

```
DebtManager/
├── Models/                 # نماذج البيانات
│   └── Debt.cs            # نموذج الديون والأطراف
├── Services/              # خدمات الأعمال
│   └── DebtService.cs     # خدمة إدارة الديون
├── Windows/               # النوافذ الفرعية
│   ├── AddDebtWindow.*    # نافذة إضافة الديون
│   ├── PartiesWindow.*    # نافذة إدارة الأطراف
│   ├── AddPartyWindow.*   # نافذة إضافة الأطراف
│   ├── CurrenciesWindow.* # نافذة إدارة العملات
│   ├── AlertsWindow.*     # نافذة التنبيهات
│   └── ReportsWindow.*    # نافذة التقارير
├── App.xaml*              # تطبيق WPF الرئيسي
├── MainWindow.xaml*       # النافذة الرئيسية
├── DatabaseHelper.cs      # مساعد قاعدة البيانات
└── DebtManager.csproj     # ملف المشروع
```

## 🗄️ قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

### جدول Debts
- معلومات الديون الأساسية
- المبالغ والعملات
- التواريخ والاستحقاقات
- الملاحظات

### جدول Parties
- بيانات الموردين والعملاء
- معلومات الاتصال
- العناوين والملاحظات

### جدول Currencies
- العملات المدعومة
- الرموز والحالة

## 🎨 واجهة المستخدم

### التصميم
- واجهة عربية بالكامل (RTL)
- تصميم عصري ومتجاوب
- ألوان تمييزية للحالات المختلفة
- أيقونات واضحة ومفهومة

### الألوان المستخدمة
- 🔵 الأزرق: العناصر الأساسية
- 🟢 الأخضر: العمليات الناجحة والديون لنا
- 🔴 الأحمر: التحذيرات والديون علينا
- 🟡 الأصفر: التنبيهات والعمليات الثانوية
- 🔵 الفيروزي: المعلومات والتقارير

## 🔧 الميزات المستقبلية

- [ ] تصدير التقارير إلى Excel/PDF
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] صلاحيات المستخدمين
- [ ] إشعارات سطح المكتب
- [ ] تطبيق ويب مصاحب
- [ ] تزامن البيانات عبر الأجهزة
- [ ] تقارير رسوم بيانية متقدمة

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى:
1. التأكد من تثبيت .NET 8.0 بشكل صحيح
2. التحقق من صلاحيات الكتابة في مجلد التطبيق
3. إنشاء issue في المستودع مع تفاصيل المشكلة

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 👨‍💻 المطور

تم تطوير هذا البرنامج باستخدام أحدث تقنيات Microsoft .NET و WPF لضمان الأداء والاستقرار.

---

**ملاحظة**: هذا البرنامج مصمم خصيصاً للبيئة العربية ويدعم اللغة العربية بالكامل مع اتجاه النص من اليمين إلى اليسار (RTL).
