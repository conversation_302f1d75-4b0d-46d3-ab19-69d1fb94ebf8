using System;

namespace DebtManager.Models
{
    public class Debt
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // مورد أو عميل
        public string Currency { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }
        public string Status { get; set; } = string.Empty; // علينا أو له
        public string Note { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public DateTime CreatedAt { get; set; }

        // خصائص محسوبة للعرض
        public string FormattedAmount => $"{Amount:N2} {Currency}";
        public string FormattedDate => Date.ToString("yyyy/MM/dd");
        public string FormattedDueDate => DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
        public string StatusDisplay => Status == "علينا" ? "مدين لنا" : "دائن علينا";
        public string TypeDisplay => Type == "مورد" ? "مورد" : "عميل";
    }

    public class Party
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // مورد أو عميل
        public string Phone { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
    }

    public class Currency
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Symbol { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }

    public class DebtSummary
    {
        public string Currency { get; set; } = string.Empty;
        public decimal TotalOwedToUs { get; set; } // ما لنا
        public decimal TotalWeOwe { get; set; } // ما علينا
        public decimal NetAmount => TotalOwedToUs - TotalWeOwe;
        public string NetStatus => NetAmount >= 0 ? "لنا" : "علينا";
        public string FormattedNet => $"{Math.Abs(NetAmount):N2} {Currency}";
    }
}
