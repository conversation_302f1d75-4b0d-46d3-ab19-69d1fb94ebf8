# DebtManager PowerShell Runner
# مشغل PowerShell لبرنامج إدارة الديون

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    DebtManager - مدير الديون" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود .NET
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم العثور على .NET SDK - الإصدار: $dotnetVersion" -ForegroundColor Green
    } else {
        throw "لم يتم العثور على .NET SDK"
    }
} catch {
    Write-Host "❌ خطأ: .NET SDK غير مثبت على النظام" -ForegroundColor Red
    Write-Host ""
    Write-Host "يرجى تحميل وتثبيت .NET 8.0 SDK من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Blue
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# بناء المشروع
Write-Host "🔨 جاري بناء المشروع..." -ForegroundColor Yellow
try {
    dotnet build DebtManager.csproj --configuration Release --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green
    } else {
        throw "فشل في بناء المشروع"
    }
} catch {
    Write-Host "❌ خطأ في بناء المشروع" -ForegroundColor Red
    Write-Host "تحقق من وجود جميع الملفات المطلوبة" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# تشغيل التطبيق
Write-Host "🚀 جاري تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "ملاحظة: قد يستغرق التشغيل الأول بعض الوقت لإنشاء قاعدة البيانات" -ForegroundColor Cyan
Write-Host ""

try {
    dotnet run --project DebtManager.csproj --configuration Release
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "تم إغلاق التطبيق" -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج"
