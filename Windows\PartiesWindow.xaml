<Window x:Class="DebtManager.Windows.PartiesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأطراف" 
        Height="500" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <TextBlock Name="HeaderText" Text="إدارة الموردين" 
                      Style="{StaticResource HeaderText}" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Name="TxtSearch" Width="200" Style="{StaticResource ModernTextBox}"
                            TextChanged="TxtSearch_TextChanged"/>
                    <Button Name="BtnAddParty" Content="➕ إضافة جديد" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource SuccessBrush}"
                           Margin="20,0,0,0"
                           Click="BtnAddParty_Click"/>
                </StackPanel>

                <!-- Parties List -->
                <DataGrid Grid.Row="1" Name="PartiesDataGrid" 
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="*"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="📝" ToolTip="تعديل" 
                                               Style="{StaticResource ModernButton}"
                                               Background="{StaticResource InfoBrush}"
                                               Width="30" Height="30"
                                               Margin="2"
                                               Click="BtnEdit_Click"/>
                                        <Button Content="🗑️" ToolTip="حذف" 
                                               Style="{StaticResource ModernButton}"
                                               Background="{StaticResource DangerBrush}"
                                               Width="30" Height="30"
                                               Margin="2"
                                               Click="BtnDelete_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{StaticResource DarkBrush}">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="جاهز" Foreground="White"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="RecordCountText" Text="0 سجل" Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
