# 🛠️ دليل التثبيت والتشغيل - DebtManager

## 📋 المتطلبات الأساسية

### 1. نظام التشغيل
- Windows 10 أو أحدث
- Windows 11 (مُوصى به)

### 2. .NET SDK
يجب تثبيت .NET 8.0 SDK أو أحدث

## 🚀 خطوات التثبيت

### الخطوة 1: تحميل وتثبيت .NET SDK

1. **زيارة الموقع الرسمي**:
   - اذهب إلى: https://dotnet.microsoft.com/download/dotnet/8.0
   
2. **تحميل SDK**:
   - اختر "Download .NET 8.0 SDK"
   - حدد النسخة المناسبة لنظامك (x64 للأنظمة 64-bit)

3. **التثبيت**:
   - شغل الملف المحمل
   - اتبع التعليمات على الشاشة
   - أعد تشغيل الكمبيوتر بعد التثبيت

### الخطوة 2: التحقق من التثبيت

1. **افتح Command Prompt**:
   - اضغط `Win + R`
   - اكتب `cmd` واضغط Enter

2. **تحقق من النسخة**:
   ```cmd
   dotnet --version
   ```
   - يجب أن تظهر نسخة .NET (مثل: 8.0.100)

### الخطوة 3: تشغيل التطبيق

#### الطريقة الأولى: استخدام ملف التشغيل السريع
1. **انقر مرتين على ملف `run.bat`**
2. سيقوم الملف بـ:
   - التحقق من وجود .NET
   - بناء المشروع
   - تشغيل التطبيق

#### الطريقة الثانية: استخدام Command Line
1. **افتح Command Prompt في مجلد المشروع**:
   ```cmd
   cd "C:\Users\<USER>\Desktop\الديون"
   ```

2. **بناء المشروع**:
   ```cmd
   dotnet build
   ```

3. **تشغيل التطبيق**:
   ```cmd
   dotnet run
   ```

#### الطريقة الثالثة: استخدام Visual Studio
1. **تثبيت Visual Studio 2022**:
   - حمل Community Edition (مجاني)
   - تأكد من تحديد ".NET desktop development"

2. **فتح المشروع**:
   - افتح Visual Studio
   - اختر "Open a project or solution"
   - حدد ملف `DebtManager.csproj`

3. **التشغيل**:
   - اضغط F5 أو اختر "Debug > Start Debugging"

## 🔧 حل المشاكل الشائعة

### مشكلة: "No .NET SDKs were found"
**الحل**:
1. تأكد من تثبيت .NET SDK (وليس Runtime فقط)
2. أعد تشغيل Command Prompt
3. أعد تشغيل الكمبيوتر إذا لزم الأمر

### مشكلة: "The application does not exist"
**الحل**:
1. تأكد من وجودك في مجلد المشروع الصحيح
2. تحقق من وجود ملف `DebtManager.csproj`

### مشكلة: خطأ في البناء (Build Error)
**الحل**:
1. تأكد من اتصال الإنترنت (لتحميل المكتبات)
2. شغل الأمر:
   ```cmd
   dotnet restore
   dotnet clean
   dotnet build
   ```

### مشكلة: لا يمكن إنشاء قاعدة البيانات
**الحل**:
1. تأكد من صلاحيات الكتابة في مجلد التطبيق
2. شغل التطبيق كمدير (Run as Administrator)

## 📁 ملفات المشروع المهمة

```
DebtManager/
├── DebtManager.csproj     # ملف المشروع الرئيسي
├── run.bat               # ملف التشغيل السريع
├── README.md             # دليل المشروع
├── INSTALL.md            # دليل التثبيت (هذا الملف)
├── App.xaml              # تطبيق WPF
├── MainWindow.xaml       # النافذة الرئيسية
├── DatabaseHelper.cs     # مساعد قاعدة البيانات
├── Models/               # نماذج البيانات
├── Services/             # خدمات الأعمال
└── Windows/              # النوافذ الفرعية
```

## 🎯 التشغيل لأول مرة

عند تشغيل التطبيق لأول مرة:

1. **إنشاء قاعدة البيانات**:
   - سيتم إنشاء ملف `debts.db` تلقائياً
   - سيتم إنشاء الجداول المطلوبة

2. **البيانات الافتراضية**:
   - سيتم إضافة العملات الأساسية (دينار، دولار، يورو)
   - الشاشة الرئيسية ستكون فارغة في البداية

3. **البدء بالاستخدام**:
   - أضف الموردين والعملاء من قائمة "الأطراف"
   - ابدأ بإدخال الديون من الشاشة الرئيسية

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من المتطلبات**:
   - .NET 8.0 SDK مثبت
   - Windows 10/11
   - صلاحيات الكتابة

2. **أعد المحاولة**:
   - أعد تشغيل Command Prompt
   - أعد تشغيل الكمبيوتر
   - شغل كمدير إذا لزم الأمر

3. **تحقق من الملفات**:
   - تأكد من وجود جميع ملفات المشروع
   - تحقق من عدم حذف أي ملفات بالخطأ

## ✅ اختبار التثبيت

للتأكد من نجاح التثبيت:

1. **شغل الأمر**:
   ```cmd
   dotnet --version
   ```
   - يجب أن تظهر نسخة .NET

2. **بناء المشروع**:
   ```cmd
   dotnet build
   ```
   - يجب أن يكتمل بدون أخطاء

3. **تشغيل التطبيق**:
   ```cmd
   dotnet run
   ```
   - يجب أن تظهر النافذة الرئيسية

---

**مبروك! 🎉 تم تثبيت DebtManager بنجاح**

يمكنك الآن البدء في إدارة ديون الموردين والعملاء بكفاءة عالية.
