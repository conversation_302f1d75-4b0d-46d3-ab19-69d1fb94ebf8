<Window x:Class="DebtManager.Windows.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير" 
        Height="600" Width="1000"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource InfoBrush}" Padding="20">
            <StackPanel>
                <TextBlock Text="📊 التقارير المالية" 
                          Style="{StaticResource HeaderText}" 
                          Foreground="White" 
                          HorizontalAlignment="Center"/>
                <TextBlock Text="تقارير شاملة عن الديون والمتابعة المالية" 
                          FontSize="14" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Report Types -->
            <Border Grid.Column="0" Background="White" CornerRadius="10" Padding="20" Margin="0,0,20,0">
                <StackPanel>
                    <TextBlock Text="أنواع التقارير" Style="{StaticResource SubHeaderText}" HorizontalAlignment="Center"/>
                    
                    <Button Name="BtnSummaryReport" Content="📈 تقرير الملخص العام" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource PrimaryBrush}"
                           Click="BtnSummaryReport_Click"/>
                    
                    <Button Name="BtnCurrencyReport" Content="💱 تقرير حسب العملة" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnCurrencyReport_Click"/>
                    
                    <Button Name="BtnPartyReport" Content="👥 تقرير حسب الطرف" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnPartyReport_Click"/>
                    
                    <Button Name="BtnDateReport" Content="📅 تقرير حسب التاريخ" 
                           Style="{StaticResource ModernButton}"
                           Click="BtnDateReport_Click"/>
                    
                    <Button Name="BtnOverdueReport" Content="⚠️ تقرير المتأخرات" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource DangerBrush}"
                           Click="BtnOverdueReport_Click"/>
                    
                    <Separator Margin="0,20"/>
                    
                    <Button Name="BtnExportExcel" Content="📄 تصدير إلى Excel" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource SuccessBrush}"
                           Click="BtnExportExcel_Click"/>
                    
                    <Button Name="BtnPrint" Content="🖨️ طباعة" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource SecondaryBrush}"
                           Click="BtnPrint_Click"/>
                </StackPanel>
            </Border>

            <!-- Report Content -->
            <Border Grid.Column="1" Background="White" CornerRadius="10" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Report Header -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,20">
                        <TextBlock Name="ReportTitle" Text="تقرير الملخص العام" 
                                  Style="{StaticResource SubHeaderText}" 
                                  HorizontalAlignment="Center"/>
                        <TextBlock Name="ReportDate" 
                                  Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='تاريخ التقرير: {0:yyyy/MM/dd HH:mm}'}" 
                                  HorizontalAlignment="Center"
                                  FontSize="12"
                                  Foreground="Gray"
                                  xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                    </StackPanel>

                    <!-- Report Content Area -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel Name="ReportContentPanel">
                            <!-- المحتوى سيتم إضافته ديناميكياً -->
                            <TextBlock Text="اختر نوع التقرير من القائمة الجانبية" 
                                      HorizontalAlignment="Center" 
                                      VerticalAlignment="Center"
                                      FontSize="16"
                                      Foreground="Gray"
                                      Margin="0,50"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{StaticResource DarkBrush}">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="جاهز" Foreground="White"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="ReportInfoText" Text="اختر تقرير" Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
