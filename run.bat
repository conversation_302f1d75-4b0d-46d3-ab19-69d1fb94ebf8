@echo off
echo ========================================
echo    DebtManager - مدير الديون
echo ========================================
echo.

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت على النظام
    echo.
    echo يرجى تحميل وتثبيت .NET 8.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo تم العثور على .NET SDK
echo.

REM بناء المشروع
echo جاري بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo تم بناء المشروع بنجاح
echo.

REM تشغيل التطبيق
echo جاري تشغيل التطبيق...
dotnet run --configuration Release

pause
