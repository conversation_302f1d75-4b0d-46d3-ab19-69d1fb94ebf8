using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Models;

namespace DebtManager.Windows
{
    public partial class PartiesWindow : Window
    {
        private readonly string _partyType;
        private List<Party> _allParties;
        private List<Party> _filteredParties;

        public PartiesWindow(string partyType)
        {
            InitializeComponent();
            _partyType = partyType;
            _allParties = new List<Party>();
            _filteredParties = new List<Party>();

            HeaderText.Text = partyType == "مورد" ? "إدارة الموردين" : "إدارة العملاء";
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                StatusText.Text = "جاري تحميل البيانات...";
                
                _allParties = GetParties();
                _filteredParties = new List<Party>(_allParties);
                
                UpdateDataGrid();
                
                StatusText.Text = "جاهز";
                RecordCountText.Text = $"{_filteredParties.Count} سجل";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في التحميل";
            }
        }

        private List<Party> GetParties()
        {
            var parties = new List<Party>();
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            string query = @"
                SELECT Id, Name, Type, Phone, Address, Notes, IsActive, CreatedAt 
                FROM Parties 
                WHERE Type = @type AND IsActive = 1
                ORDER BY Name";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@type", _partyType);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                parties.Add(new Party
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Type = reader.GetString("Type"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    Address = reader.IsDBNull("Address") ? "" : reader.GetString("Address"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    IsActive = reader.GetInt32("IsActive") == 1,
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return parties;
        }

        private void UpdateDataGrid()
        {
            PartiesDataGrid.ItemsSource = null;
            PartiesDataGrid.ItemsSource = _filteredParties;
        }

        private void FilterParties()
        {
            var searchText = TxtSearch.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredParties = new List<Party>(_allParties);
            }
            else
            {
                _filteredParties = _allParties.Where(p => 
                    p.Name.ToLower().Contains(searchText) ||
                    p.Phone.ToLower().Contains(searchText) ||
                    p.Address.ToLower().Contains(searchText) ||
                    p.Notes.ToLower().Contains(searchText)
                ).ToList();
            }

            UpdateDataGrid();
            RecordCountText.Text = $"{_filteredParties.Count} سجل";
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterParties();
        }

        private void BtnAddParty_Click(object sender, RoutedEventArgs e)
        {
            var addPartyWindow = new AddPartyWindow(_partyType);
            if (addPartyWindow.ShowDialog() == true)
            {
                LoadData();
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is Party selectedParty)
            {
                var editPartyWindow = new AddPartyWindow(_partyType, selectedParty);
                if (editPartyWindow.ShowDialog() == true)
                {
                    LoadData();
                }
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is Party selectedParty)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف {selectedParty.Name}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    if (DeleteParty(selectedParty.Id))
                    {
                        MessageBox.Show("تم حذف السجل بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف السجل", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private bool DeleteParty(int partyId)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // التحقق من وجود ديون مرتبطة بهذا الطرف
                string checkQuery = "SELECT COUNT(*) FROM Debts WHERE Name = (SELECT Name FROM Parties WHERE Id = @id)";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@id", partyId);
                var debtCount = Convert.ToInt32(checkCommand.ExecuteScalar());

                if (debtCount > 0)
                {
                    MessageBox.Show("لا يمكن حذف هذا الطرف لأن له ديون مسجلة", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // حذف الطرف (تعطيل فقط)
                string deleteQuery = "UPDATE Parties SET IsActive = 0 WHERE Id = @id";
                using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@id", partyId);
                deleteCommand.ExecuteNonQuery();

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
