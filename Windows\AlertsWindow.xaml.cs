using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Models;
using DebtManager.Services;

namespace DebtManager.Windows
{
    public partial class AlertsWindow : Window
    {
        private readonly DebtService _debtService;
        private List<DebtAlert> _alerts;

        public AlertsWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            _alerts = new List<DebtAlert>();
            
            CmbDaysFilter.SelectedIndex = 0; // الأسبوع القادم افتراضياً
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                StatusText.Text = "جاري تحميل التنبيهات...";
                
                var selectedItem = (ComboBoxItem)CmbDaysFilter.SelectedItem;
                var daysAhead = int.Parse(selectedItem.Tag.ToString()!);
                
                var upcomingDebts = _debtService.GetUpcomingDueDates(daysAhead);
                _alerts = upcomingDebts.Select(d => new DebtAlert(d)).ToList();
                
                UpdateDataGrid();
                
                StatusText.Text = "جاهز";
                AlertCountText.Text = $"{_alerts.Count} تنبيه";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في التحميل";
            }
        }

        private void UpdateDataGrid()
        {
            AlertsDataGrid.ItemsSource = null;
            AlertsDataGrid.ItemsSource = _alerts;
        }

        private void CmbDaysFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                LoadData();
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }
    }

    // فئة مساعدة لعرض التنبيهات
    public class DebtAlert : Debt
    {
        public DebtAlert(Debt debt)
        {
            Id = debt.Id;
            Name = debt.Name;
            Type = debt.Type;
            Currency = debt.Currency;
            Amount = debt.Amount;
            Date = debt.Date;
            Status = debt.Status;
            Note = debt.Note;
            DueDate = debt.DueDate;
            CreatedAt = debt.CreatedAt;
        }

        public bool IsOverdue => DueDate.HasValue && DueDate.Value.Date < DateTime.Today;
        
        public string DueStatus
        {
            get
            {
                if (!DueDate.HasValue) return "غير محدد";
                
                var daysUntilDue = (DueDate.Value.Date - DateTime.Today).Days;
                
                if (daysUntilDue < 0)
                    return $"متأخر {Math.Abs(daysUntilDue)} يوم";
                else if (daysUntilDue == 0)
                    return "مستحق اليوم";
                else if (daysUntilDue <= 3)
                    return $"خلال {daysUntilDue} أيام";
                else
                    return $"خلال {daysUntilDue} يوم";
            }
        }
    }
}
