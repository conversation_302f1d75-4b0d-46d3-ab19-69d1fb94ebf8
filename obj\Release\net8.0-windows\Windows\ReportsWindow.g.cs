﻿#pragma checksum "..\..\..\..\Windows\ReportsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4DFEC27E3CB392403D60A3ABDD65638830035006"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Windows {
    
    
    /// <summary>
    /// ReportsWindow
    /// </summary>
    public partial class ReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSummaryReport;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCurrencyReport;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPartyReport;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDateReport;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOverdueReport;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrint;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitle;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportDate;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContentPanel;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportInfoText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/windows/reportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnSummaryReport = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnSummaryReport.Click += new System.Windows.RoutedEventHandler(this.BtnSummaryReport_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnCurrencyReport = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnCurrencyReport.Click += new System.Windows.RoutedEventHandler(this.BtnCurrencyReport_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnPartyReport = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnPartyReport.Click += new System.Windows.RoutedEventHandler(this.BtnPartyReport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnDateReport = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnDateReport.Click += new System.Windows.RoutedEventHandler(this.BtnDateReport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnOverdueReport = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnOverdueReport.Click += new System.Windows.RoutedEventHandler(this.BtnOverdueReport_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\..\Windows\ReportsWindow.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ReportTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ReportDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ReportContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ReportInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

