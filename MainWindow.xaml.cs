using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DebtManager.Models;
using DebtManager.Services;
using DebtManager.Windows;

namespace DebtManager
{
    public partial class MainWindow : Window
    {
        private readonly DebtService _debtService;
        private List<Debt> _allDebts;
        private List<Debt> _filteredDebts;

        public MainWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            _allDebts = new List<Debt>();
            _filteredDebts = new List<Debt>();
            
            InitializeFilters();
            LoadData();
        }

        private void InitializeFilters()
        {
            // تهيئة فلاتر العملات
            CmbFilterCurrency.Items.Add("جميع العملات");
            var currencies = DatabaseHelper.GetCurrencies();
            foreach (var currency in currencies)
            {
                CmbFilterCurrency.Items.Add(currency);
            }
            CmbFilterCurrency.SelectedIndex = 0;

            // تهيئة فلاتر النوع
            CmbFilterType.Items.Add("الكل");
            CmbFilterType.Items.Add("مورد");
            CmbFilterType.Items.Add("عميل");
            CmbFilterType.SelectedIndex = 0;

            // تهيئة فلاتر الحالة
            CmbFilterStatus.Items.Add("الكل");
            CmbFilterStatus.Items.Add("علينا");
            CmbFilterStatus.Items.Add("له");
            CmbFilterStatus.SelectedIndex = 0;
        }

        private void LoadData()
        {
            try
            {
                StatusText.Text = "جاري تحميل البيانات...";
                
                _allDebts = _debtService.GetAllDebts();
                _filteredDebts = new List<Debt>(_allDebts);
                
                UpdateDataGrid();
                UpdateSummaryCards();
                
                StatusText.Text = "جاهز";
                RecordCountText.Text = $"{_filteredDebts.Count} سجل";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في التحميل";
            }
        }

        private void UpdateDataGrid()
        {
            DebtsDataGrid.ItemsSource = null;
            DebtsDataGrid.ItemsSource = _filteredDebts;
        }

        private void UpdateSummaryCards()
        {
            SummaryCardsPanel.Children.Clear();
            
            var summaries = _debtService.GetDebtSummary();
            
            foreach (var summary in summaries)
            {
                var card = CreateSummaryCard(summary);
                SummaryCardsPanel.Children.Add(card);
            }
        }

        private Border CreateSummaryCard(DebtSummary summary)
        {
            var card = new Border
            {
                Background = summary.NetAmount >= 0 ? 
                    (Brush)FindResource("SuccessBrush") : 
                    (Brush)FindResource("DangerBrush"),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 10, 0),
                MinWidth = 200
            };

            var stackPanel = new StackPanel();
            
            var currencyText = new TextBlock
            {
                Text = summary.Currency,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            
            var amountText = new TextBlock
            {
                Text = summary.FormattedNet,
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            
            var statusText = new TextBlock
            {
                Text = summary.NetStatus,
                FontSize = 14,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Opacity = 0.9
            };

            stackPanel.Children.Add(currencyText);
            stackPanel.Children.Add(amountText);
            stackPanel.Children.Add(statusText);
            
            card.Child = stackPanel;
            return card;
        }

        private void ApplyFilters()
        {
            _filteredDebts = new List<Debt>(_allDebts);

            // تصفية العملة
            if (CmbFilterCurrency.SelectedIndex > 0)
            {
                var selectedCurrency = CmbFilterCurrency.SelectedItem.ToString();
                _filteredDebts = _filteredDebts.Where(d => d.Currency == selectedCurrency).ToList();
            }

            // تصفية النوع
            if (CmbFilterType.SelectedIndex > 0)
            {
                var selectedType = CmbFilterType.SelectedItem.ToString();
                _filteredDebts = _filteredDebts.Where(d => d.Type == selectedType).ToList();
            }

            // تصفية الحالة
            if (CmbFilterStatus.SelectedIndex > 0)
            {
                var selectedStatus = CmbFilterStatus.SelectedItem.ToString();
                _filteredDebts = _filteredDebts.Where(d => d.Status == selectedStatus).ToList();
            }

            UpdateDataGrid();
            RecordCountText.Text = $"{_filteredDebts.Count} سجل";
        }

        // Event Handlers
        private void BtnAddDebt_Click(object sender, RoutedEventArgs e)
        {
            var addDebtWindow = new AddDebtWindow();
            if (addDebtWindow.ShowDialog() == true)
            {
                LoadData();
            }
        }

        private void BtnSuppliers_Click(object sender, RoutedEventArgs e)
        {
            var partiesWindow = new PartiesWindow("مورد");
            partiesWindow.ShowDialog();
        }

        private void BtnCustomers_Click(object sender, RoutedEventArgs e)
        {
            var partiesWindow = new PartiesWindow("عميل");
            partiesWindow.ShowDialog();
        }

        private void BtnCurrencies_Click(object sender, RoutedEventArgs e)
        {
            var currenciesWindow = new CurrenciesWindow();
            if (currenciesWindow.ShowDialog() == true)
            {
                InitializeFilters();
                LoadData();
            }
        }

        private void BtnAlerts_Click(object sender, RoutedEventArgs e)
        {
            var alertsWindow = new AlertsWindow();
            alertsWindow.ShowDialog();
        }

        private void BtnReports_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void CmbFilterCurrency_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded) ApplyFilters();
        }

        private void CmbFilterType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded) ApplyFilters();
        }

        private void CmbFilterStatus_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded) ApplyFilters();
        }

        private void DebtsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (DebtsDataGrid.SelectedItem is Debt selectedDebt)
            {
                var editWindow = new AddDebtWindow(selectedDebt);
                if (editWindow.ShowDialog() == true)
                {
                    LoadData();
                }
            }
        }
    }
}
