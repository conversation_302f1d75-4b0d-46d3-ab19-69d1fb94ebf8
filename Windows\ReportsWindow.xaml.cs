using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DebtManager.Models;
using DebtManager.Services;

namespace DebtManager.Windows
{
    public partial class ReportsWindow : Window
    {
        private readonly DebtService _debtService;

        public ReportsWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            
            // عرض التقرير الافتراضي
            GenerateSummaryReport();
        }

        private void BtnSummaryReport_Click(object sender, RoutedEventArgs e)
        {
            GenerateSummaryReport();
        }

        private void GenerateSummaryReport()
        {
            try
            {
                StatusText.Text = "جاري إنشاء تقرير الملخص العام...";
                ReportTitle.Text = "تقرير الملخص العام";
                
                ReportContentPanel.Children.Clear();
                
                var summaries = _debtService.GetDebtSummary();
                var allDebts = _debtService.GetAllDebts();
                
                // إحصائيات عامة
                var statsPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };
                
                statsPanel.Children.Add(CreateSectionHeader("الإحصائيات العامة"));
                statsPanel.Children.Add(CreateInfoRow("إجمالي السجلات:", allDebts.Count.ToString()));
                statsPanel.Children.Add(CreateInfoRow("عدد الموردين:", allDebts.Where(d => d.Type == "مورد").Select(d => d.Name).Distinct().Count().ToString()));
                statsPanel.Children.Add(CreateInfoRow("عدد العملاء:", allDebts.Where(d => d.Type == "عميل").Select(d => d.Name).Distinct().Count().ToString()));
                
                ReportContentPanel.Children.Add(statsPanel);
                
                // ملخص حسب العملة
                var currencyPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };
                currencyPanel.Children.Add(CreateSectionHeader("ملخص الديون حسب العملة"));
                
                foreach (var summary in summaries)
                {
                    var card = CreateSummaryCard(summary);
                    currencyPanel.Children.Add(card);
                }
                
                ReportContentPanel.Children.Add(currencyPanel);
                
                StatusText.Text = "تم إنشاء التقرير بنجاح";
                ReportInfoText.Text = $"تقرير الملخص - {summaries.Count} عملة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في إنشاء التقرير";
            }
        }

        private void BtnCurrencyReport_Click(object sender, RoutedEventArgs e)
        {
            GenerateCurrencyReport();
        }

        private void GenerateCurrencyReport()
        {
            try
            {
                StatusText.Text = "جاري إنشاء تقرير العملات...";
                ReportTitle.Text = "تقرير مفصل حسب العملة";
                
                ReportContentPanel.Children.Clear();
                
                var allDebts = _debtService.GetAllDebts();
                var currencies = allDebts.Select(d => d.Currency).Distinct().OrderBy(c => c);
                
                foreach (var currency in currencies)
                {
                    var currencyDebts = allDebts.Where(d => d.Currency == currency).ToList();
                    var currencyPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 30) };
                    
                    currencyPanel.Children.Add(CreateSectionHeader($"عملة: {currency}"));
                    
                    var owedToUs = currencyDebts.Where(d => d.Status == "له").Sum(d => d.Amount);
                    var weOwe = currencyDebts.Where(d => d.Status == "علينا").Sum(d => d.Amount);
                    var net = owedToUs - weOwe;
                    
                    currencyPanel.Children.Add(CreateInfoRow("ما لنا:", $"{owedToUs:N2} {currency}"));
                    currencyPanel.Children.Add(CreateInfoRow("ما علينا:", $"{weOwe:N2} {currency}"));
                    currencyPanel.Children.Add(CreateInfoRow("الصافي:", $"{Math.Abs(net):N2} {currency} ({(net >= 0 ? "لنا" : "علينا")})"));
                    currencyPanel.Children.Add(CreateInfoRow("عدد السجلات:", currencyDebts.Count.ToString()));
                    
                    ReportContentPanel.Children.Add(currencyPanel);
                }
                
                StatusText.Text = "تم إنشاء التقرير بنجاح";
                ReportInfoText.Text = $"تقرير العملات - {currencies.Count()} عملة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في إنشاء التقرير";
            }
        }

        private void BtnPartyReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير الأطراف - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnDateReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير التواريخ - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnOverdueReport_Click(object sender, RoutedEventArgs e)
        {
            GenerateOverdueReport();
        }

        private void GenerateOverdueReport()
        {
            try
            {
                StatusText.Text = "جاري إنشاء تقرير المتأخرات...";
                ReportTitle.Text = "تقرير الديون المتأخرة";
                
                ReportContentPanel.Children.Clear();
                
                var overdueDebts = _debtService.GetAllDebts()
                    .Where(d => d.DueDate.HasValue && d.DueDate.Value.Date < DateTime.Today)
                    .OrderBy(d => d.DueDate)
                    .ToList();
                
                if (!overdueDebts.Any())
                {
                    ReportContentPanel.Children.Add(new TextBlock
                    {
                        Text = "🎉 لا توجد ديون متأخرة",
                        FontSize = 18,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 50),
                        Foreground = new SolidColorBrush(Colors.Green)
                    });
                }
                else
                {
                    var overduePanel = new StackPanel();
                    overduePanel.Children.Add(CreateSectionHeader($"الديون المتأخرة ({overdueDebts.Count} دين)"));
                    
                    foreach (var debt in overdueDebts)
                    {
                        var daysOverdue = (DateTime.Today - debt.DueDate!.Value.Date).Days;
                        var debtInfo = new StackPanel 
                        { 
                            Orientation = Orientation.Horizontal, 
                            Margin = new Thickness(0, 5) 
                        };
                        
                        debtInfo.Children.Add(new TextBlock 
                        { 
                            Text = $"• {debt.Name} - {debt.FormattedAmount} - متأخر {daysOverdue} يوم",
                            Margin = new Thickness(0, 0, 10, 0)
                        });
                        
                        overduePanel.Children.Add(debtInfo);
                    }
                    
                    ReportContentPanel.Children.Add(overduePanel);
                }
                
                StatusText.Text = "تم إنشاء التقرير بنجاح";
                ReportInfoText.Text = $"تقرير المتأخرات - {overdueDebts.Count} دين";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في إنشاء التقرير";
            }
        }

        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تصدير Excel - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الطباعة - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Helper methods
        private TextBlock CreateSectionHeader(string text)
        {
            return new TextBlock
            {
                Text = text,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10),
                Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
            };
        }

        private StackPanel CreateInfoRow(string label, string value)
        {
            var panel = new StackPanel 
            { 
                Orientation = Orientation.Horizontal, 
                Margin = new Thickness(0, 2) 
            };
            
            panel.Children.Add(new TextBlock 
            { 
                Text = label, 
                FontWeight = FontWeights.SemiBold,
                Width = 120
            });
            
            panel.Children.Add(new TextBlock { Text = value });
            
            return panel;
        }

        private Border CreateSummaryCard(DebtSummary summary)
        {
            var card = new Border
            {
                Background = summary.NetAmount >= 0 ? 
                    new SolidColorBrush(Color.FromRgb(76, 175, 80)) : 
                    new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(15, 10),
                Margin = new Thickness(0, 5),
                HorizontalAlignment = HorizontalAlignment.Left
            };

            var stackPanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            stackPanel.Children.Add(new TextBlock
            {
                Text = $"{summary.Currency}: {summary.FormattedNet} ({summary.NetStatus})",
                Foreground = Brushes.White,
                FontWeight = FontWeights.SemiBold
            });
            
            card.Child = stackPanel;
            return card;
        }
    }
}
