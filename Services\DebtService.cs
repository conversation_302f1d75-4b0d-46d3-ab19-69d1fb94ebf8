using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class DebtService
    {
        public List<Debt> GetAllDebts()
        {
            var debts = new List<Debt>();
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            string query = @"
                SELECT Id, Name, Type, Currency, Amount, Date, Status, Note, DueDate, CreatedAt 
                FROM Debts 
                ORDER BY Date DESC";

            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                debts.Add(new Debt
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Type = reader.GetString("Type"),
                    Currency = reader.GetString("Currency"),
                    Amount = reader.GetDecimal("Amount"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    Status = reader.GetString("Status"),
                    Note = reader.IsDBNull("Note") ? "" : reader.GetString("Note"),
                    DueDate = reader.IsDBNull("DueDate") ? null : DateTime.Parse(reader.GetString("DueDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return debts;
        }

        public List<Debt> GetDebtsByParty(string partyName)
        {
            var debts = new List<Debt>();
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            string query = @"
                SELECT Id, Name, Type, Currency, Amount, Date, Status, Note, DueDate, CreatedAt 
                FROM Debts 
                WHERE Name = @name
                ORDER BY Date DESC";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@name", partyName);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                debts.Add(new Debt
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Type = reader.GetString("Type"),
                    Currency = reader.GetString("Currency"),
                    Amount = reader.GetDecimal("Amount"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    Status = reader.GetString("Status"),
                    Note = reader.IsDBNull("Note") ? "" : reader.GetString("Note"),
                    DueDate = reader.IsDBNull("DueDate") ? null : DateTime.Parse(reader.GetString("DueDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return debts;
        }

        public List<DebtSummary> GetDebtSummary()
        {
            var summaries = new List<DebtSummary>();
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            string query = @"
                SELECT 
                    Currency,
                    SUM(CASE WHEN Status = 'له' THEN Amount ELSE 0 END) as TotalOwedToUs,
                    SUM(CASE WHEN Status = 'علينا' THEN Amount ELSE 0 END) as TotalWeOwe
                FROM Debts 
                GROUP BY Currency
                ORDER BY Currency";

            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                summaries.Add(new DebtSummary
                {
                    Currency = reader.GetString("Currency"),
                    TotalOwedToUs = reader.GetDecimal("TotalOwedToUs"),
                    TotalWeOwe = reader.GetDecimal("TotalWeOwe")
                });
            }

            return summaries;
        }

        public bool AddDebt(Debt debt)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"
                    INSERT INTO Debts (Name, Type, Currency, Amount, Date, Status, Note, DueDate)
                    VALUES (@name, @type, @currency, @amount, @date, @status, @note, @dueDate)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@name", debt.Name);
                command.Parameters.AddWithValue("@type", debt.Type);
                command.Parameters.AddWithValue("@currency", debt.Currency);
                command.Parameters.AddWithValue("@amount", debt.Amount);
                command.Parameters.AddWithValue("@date", debt.Date.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@status", debt.Status);
                command.Parameters.AddWithValue("@note", debt.Note ?? "");
                command.Parameters.AddWithValue("@dueDate", debt.DueDate?.ToString("yyyy-MM-dd") ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool UpdateDebt(Debt debt)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"
                    UPDATE Debts 
                    SET Name = @name, Type = @type, Currency = @currency, Amount = @amount, 
                        Date = @date, Status = @status, Note = @note, DueDate = @dueDate
                    WHERE Id = @id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", debt.Id);
                command.Parameters.AddWithValue("@name", debt.Name);
                command.Parameters.AddWithValue("@type", debt.Type);
                command.Parameters.AddWithValue("@currency", debt.Currency);
                command.Parameters.AddWithValue("@amount", debt.Amount);
                command.Parameters.AddWithValue("@date", debt.Date.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@status", debt.Status);
                command.Parameters.AddWithValue("@note", debt.Note ?? "");
                command.Parameters.AddWithValue("@dueDate", debt.DueDate?.ToString("yyyy-MM-dd") ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteDebt(int debtId)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "DELETE FROM Debts WHERE Id = @id";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", debtId);

                command.ExecuteNonQuery();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public List<Debt> GetUpcomingDueDates(int daysAhead = 7)
        {
            var debts = new List<Debt>();
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            string query = @"
                SELECT Id, Name, Type, Currency, Amount, Date, Status, Note, DueDate, CreatedAt 
                FROM Debts 
                WHERE DueDate IS NOT NULL 
                AND date(DueDate) BETWEEN date('now') AND date('now', '+' || @days || ' days')
                ORDER BY DueDate ASC";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@days", daysAhead);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                debts.Add(new Debt
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Type = reader.GetString("Type"),
                    Currency = reader.GetString("Currency"),
                    Amount = reader.GetDecimal("Amount"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    Status = reader.GetString("Status"),
                    Note = reader.IsDBNull("Note") ? "" : reader.GetString("Note"),
                    DueDate = DateTime.Parse(reader.GetString("DueDate")),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"))
                });
            }

            return debts;
        }
    }
}
