using System;
using System.Data.SQLite;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DebtManager.Models;
using DebtManager.Services;

namespace DebtManager.Windows
{
    public partial class AddDebtWindow : Window
    {
        private readonly DebtService _debtService;
        private readonly Debt? _editingDebt;
        private readonly bool _isEditMode;

        public AddDebtWindow(Debt? debtToEdit = null)
        {
            InitializeComponent();
            _debtService = new DebtService();
            _editingDebt = debtToEdit;
            _isEditMode = debtToEdit != null;

            InitializeForm();
            
            if (_isEditMode)
            {
                LoadDebtData();
                HeaderText.Text = "تعديل الحساب";
                BtnDelete.Visibility = Visibility.Visible;
            }
        }

        private void InitializeForm()
        {
            // تحميل العملات
            var currencies = DatabaseHelper.GetCurrencies();
            foreach (var currency in currencies)
            {
                CmbCurrency.Items.Add(currency);
            }

            // تحميل أسماء الأطراف
            LoadPartyNames();

            // تعيين القيم الافتراضية
            DpDate.SelectedDate = DateTime.Today;
            RbSupplier.IsChecked = true;
            CmbStatus.SelectedIndex = 0;
        }

        private void LoadPartyNames()
        {
            CmbPartyName.Items.Clear();
            
            var suppliers = DatabaseHelper.GetParties("مورد");
            var customers = DatabaseHelper.GetParties("عميل");
            
            foreach (var supplier in suppliers)
            {
                CmbPartyName.Items.Add(supplier);
            }
            
            foreach (var customer in customers)
            {
                CmbPartyName.Items.Add(customer);
            }
        }

        private void LoadDebtData()
        {
            if (_editingDebt == null) return;

            CmbPartyName.Text = _editingDebt.Name;
            RbSupplier.IsChecked = _editingDebt.Type == "مورد";
            RbCustomer.IsChecked = _editingDebt.Type == "عميل";
            
            CmbCurrency.SelectedItem = _editingDebt.Currency;
            TxtAmount.Text = _editingDebt.Amount.ToString("F2");
            
            foreach (ComboBoxItem item in CmbStatus.Items)
            {
                if (item.Tag.ToString() == _editingDebt.Status)
                {
                    CmbStatus.SelectedItem = item;
                    break;
                }
            }
            
            DpDate.SelectedDate = _editingDebt.Date;
            DpDueDate.SelectedDate = _editingDebt.DueDate;
            TxtNotes.Text = _editingDebt.Note;
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(CmbPartyName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الطرف", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbPartyName.Focus();
                return false;
            }

            if (!RbSupplier.IsChecked.Value && !RbCustomer.IsChecked.Value)
            {
                MessageBox.Show("يرجى اختيار نوع الطرف", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (CmbCurrency.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العملة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbCurrency.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtAmount.Text) || 
                !decimal.TryParse(TxtAmount.Text, out decimal amount) || 
                amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            if (CmbStatus.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الدين", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbStatus.Focus();
                return false;
            }

            if (DpDate.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار التاريخ", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpDate.Focus();
                return false;
            }

            return true;
        }

        private void SavePartyIfNew()
        {
            var partyName = CmbPartyName.Text.Trim();
            var partyType = RbSupplier.IsChecked.Value ? "مورد" : "عميل";

            // التحقق من وجود الطرف
            var existingParties = DatabaseHelper.GetParties();
            if (existingParties.Contains(partyName))
                return;

            // إضافة طرف جديد
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"
                    INSERT INTO Parties (Name, Type, Phone, Address, Notes)
                    VALUES (@name, @type, @phone, @address, @notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@name", partyName);
                command.Parameters.AddWithValue("@type", partyType);
                command.Parameters.AddWithValue("@phone", TxtPhone.Text.Trim());
                command.Parameters.AddWithValue("@address", TxtAddress.Text.Trim());
                command.Parameters.AddWithValue("@notes", TxtPartyNotes.Text.Trim());

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الطرف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                // حفظ الطرف إذا كان جديداً
                SavePartyIfNew();

                var debt = new Debt
                {
                    Name = CmbPartyName.Text.Trim(),
                    Type = RbSupplier.IsChecked.Value ? "مورد" : "عميل",
                    Currency = CmbCurrency.SelectedItem.ToString()!,
                    Amount = decimal.Parse(TxtAmount.Text),
                    Status = ((ComboBoxItem)CmbStatus.SelectedItem).Tag.ToString()!,
                    Date = DpDate.SelectedDate!.Value,
                    DueDate = DpDueDate.SelectedDate,
                    Note = TxtNotes.Text.Trim()
                };

                bool success;
                if (_isEditMode)
                {
                    debt.Id = _editingDebt!.Id;
                    success = _debtService.UpdateDebt(debt);
                }
                else
                {
                    success = _debtService.AddDebt(debt);
                }

                if (success)
                {
                    MessageBox.Show(_isEditMode ? "تم تحديث الحساب بنجاح" : "تم إضافة الحساب بنجاح", 
                        "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ البيانات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (_editingDebt == null) return;

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا السجل؟", "تأكيد الحذف", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                if (_debtService.DeleteDebt(_editingDebt.Id))
                {
                    MessageBox.Show("تم حذف السجل بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حذف السجل", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void TxtAmount_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // السماح بالأرقام والنقطة العشرية فقط
            Regex regex = new Regex(@"^[0-9]*\.?[0-9]*$");
            e.Handled = !regex.IsMatch(((TextBox)sender).Text + e.Text);
        }

        private void CmbPartyName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // إخفاء تفاصيل الطرف الجديد إذا تم اختيار طرف موجود
            if (CmbPartyName.SelectedItem != null)
            {
                PartyDetailsExpander.IsExpanded = false;
            }
        }

        private void PartyType_Checked(object sender, RoutedEventArgs e)
        {
            // تحديث قائمة الأطراف حسب النوع المختار
            if (IsLoaded)
            {
                LoadPartyNames();
            }
        }
    }
}
