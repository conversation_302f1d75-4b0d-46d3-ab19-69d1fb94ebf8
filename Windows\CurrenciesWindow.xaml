<Window x:Class="DebtManager.Windows.CurrenciesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة العملات" 
        Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <TextBlock Text="إدارة العملات" 
                      Style="{StaticResource HeaderText}" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Add Currency Form -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBlock Text="اسم العملة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Name="TxtCurrencyName" Width="150" Style="{StaticResource ModernTextBox}"/>
                    <TextBlock Text="الرمز:" VerticalAlignment="Center" Margin="20,0,10,0"/>
                    <TextBox Name="TxtCurrencySymbol" Width="80" Style="{StaticResource ModernTextBox}"/>
                    <Button Name="BtnAddCurrency" Content="➕ إضافة" 
                           Style="{StaticResource ModernButton}"
                           Background="{StaticResource SuccessBrush}"
                           Margin="20,0,0,0"
                           Click="BtnAddCurrency_Click"/>
                </StackPanel>

                <!-- Currencies List -->
                <DataGrid Grid.Row="1" Name="CurrenciesDataGrid" 
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم العملة" Binding="{Binding Name}" Width="*"/>
                        <DataGridTextColumn Header="الرمز" Binding="{Binding Symbol}" Width="100"/>
                        <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="80"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="🗑️" ToolTip="حذف" 
                                               Style="{StaticResource ModernButton}"
                                               Background="{StaticResource DangerBrush}"
                                               Width="30" Height="30"
                                               Margin="2"
                                               Click="BtnDelete_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
            <Button Name="BtnClose" Content="إغلاق" 
                   Style="{StaticResource ModernButton}"
                   Width="120"
                   HorizontalAlignment="Center"
                   Click="BtnClose_Click"/>
        </Border>
    </Grid>
</Window>
